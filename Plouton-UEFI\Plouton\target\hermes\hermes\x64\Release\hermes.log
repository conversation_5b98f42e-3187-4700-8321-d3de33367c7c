﻿  hermes.c
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(29,2): error C2061: 语法错误: 标识符“UINT64”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(30,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(34,2): error C2061: 语法错误: 标识符“WinExport”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(36,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(40,2): error C2061: 语法错误: 标识符“UINT64”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(41,9): error C2061: 语法错误: 标识符“imageFileName”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(41,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(42,9): error C2061: 语法错误: 标识符“stackCount”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(42,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(43,9): error C2061: 语法错误: 标识符“dirBase”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(43,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(44,9): error C2061: 语法错误: 标识符“peb”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(44,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(45,9): error C2061: 语法错误: 标识符“virtualSize”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(45,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(46,9): error C2061: 语法错误: 标识符“vadroot”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(46,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(47,9): error C2061: 语法错误: 标识符“imageFilePointer”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(47,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(48,9): error C2061: 语法错误: 标识符“fileName”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(48,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(49,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(53,2): error C2061: 语法错误: 标识符“UINT64”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(54,9): error C2061: 语法错误: 标识符“mapsSize”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(54,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(55,8): error C2061: 语法错误: 标识符“pid”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(55,8): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(56,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(60,2): error C2061: 语法错误: 标识符“UINT64”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(61,9): error C2061: 语法错误: 标识符“entryPoint”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(61,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(62,9): error C2061: 语法错误: 标识符“sizeOfModule”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(62,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(65,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(69,2): error C2061: 语法错误: 标识符“UINT64”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(70,9): error C2061: 语法错误: 标识符“physProcess”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(70,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(71,9): error C2061: 语法错误: 标识符“dirBase”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(71,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(72,9): error C2061: 语法错误: 标识符“pid”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(72,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(73,9): error C2061: 语法错误: 标识符“size”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(73,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(74,9): error C2061: 语法错误: 标识符“vadRoot”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(74,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(75,7): error C2372: “name”: 重定义；不同的间接寻址类型
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(63): message : 参见“name”的声明
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(76,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(80,2): error C2061: 语法错误: 标识符“ProcessData”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(81,13): error C2061: 语法错误: 标识符“offsets”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(81,13): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(82,9): error C2061: 语法错误: 标识符“ntKernel”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(82,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(83,9): error C2061: 语法错误: 标识符“ntVersion”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(83,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(84,9): error C2061: 语法错误: 标识符“ntBuild”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(84,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(85,16): error C2061: 语法错误: 标识符“ntExports”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(85,16): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(86,10): error C2061: 语法错误: 标识符“initialProcess”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(86,10): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(87,10): error C2061: 语法错误: 标识符“gameProcess”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(87,10): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(88,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(92,2): error C2061: 语法错误: 标识符“UINT16”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(93,9): error C2061: 语法错误: 标识符“e_cblp”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(93,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(94,9): error C2061: 语法错误: 标识符“e_cp”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(94,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(95,9): error C2061: 语法错误: 标识符“e_crlc”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(95,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(96,9): error C2061: 语法错误: 标识符“e_cparhdr”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(96,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(97,9): error C2061: 语法错误: 标识符“e_minalloc”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(97,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(98,9): error C2061: 语法错误: 标识符“e_maxalloc”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(98,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(99,9): error C2061: 语法错误: 标识符“e_ss”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(99,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(100,9): error C2061: 语法错误: 标识符“e_sp”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(100,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(101,9): error C2061: 语法错误: 标识符“e_csum”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(101,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(102,9): error C2061: 语法错误: 标识符“e_ip”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(102,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(103,9): error C2061: 语法错误: 标识符“e_cs”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(103,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(104,9): error C2061: 语法错误: 标识符“e_lfarlc”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(104,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(105,9): error C2061: 语法错误: 标识符“e_ovno”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(105,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(106,9): error C2061: 语法错误: 标识符“e_res”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(106,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(106,14): error C2059: 语法错误:“[”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(107,9): error C2061: 语法错误: 标识符“e_oemid”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(107,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(108,9): error C2061: 语法错误: 标识符“e_oeminfo”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(108,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(109,9): error C2061: 语法错误: 标识符“e_res2”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(109,9): error C2059: 语法错误:“;”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(109,15): error C2059: 语法错误:“[”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(111,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\plouton-main\Plouton-UEFI\Plouton\os\windows\windows.h(111,1): fatal error C1003: 错误计数超过 100；正在停止编译
